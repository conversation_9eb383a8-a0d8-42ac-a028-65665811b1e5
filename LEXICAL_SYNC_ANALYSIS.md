# Lexical Format Sync Analysis for Ghost Integration

## Executive Summary

**YES, using lexical format for Ghost syncing is not only possible but highly recommended.** The TryGhost libraries already provide excellent markdown → lexical conversion that produces stable, Ghost-compatible lexical structures. This would be the most reliable approach for syncing content.

## Current State Analysis

### What's Already Working

1. **CLI Scripts (`scripts/ghost.js` and `lib/content-converter.js`)**:
   - ✅ Already use `@tryghost/kg-html-to-lexical` and `@tryghost/kg-markdown-html-renderer`
   - ✅ Successfully convert: markdown → HTML → lexical
   - ✅ Produce proper Ghost-compatible lexical structures
   - ✅ Handle all markdown features including code blocks, lists, headings, etc.

2. **Obsidian Plugin (`obsidian-ghost-sync`)**:
   - ❌ Currently uses custom markdown → HTML conversion
   - ❌ Sets `lexical: null` and relies on `source: 'html'`
   - ❌ Less stable and reliable than lexical approach

### Test Results

The TryGhost approach produces excellent lexical structures:

```json
{
  "type": "codeblock",
  "version": 1,
  "code": "function hello() {\n  console.log(\"Hello world!\");\n}\n",
  "language": "javascript",
  "caption": ""
}
```

- <PERSON><PERSON> 14 different node types correctly
- Preserves code block languages
- Maintains proper text formatting (bold, italic, inline code)
- Supports lists, headings, blockquotes, horizontal rules

## Benefits of Lexical Format Sync

### 1. **Stability and Reliability**
- Lexical is Ghost's native editor format
- No conversion artifacts or data loss
- Consistent round-trip conversion (Ghost → local → Ghost)

### 2. **Advanced Feature Support**
- Better handling of complex markdown syntax
- Support for callouts/blockquotes with proper formatting
- Accurate preservation of code blocks with syntax highlighting
- Proper handling of nested lists and complex structures

### 3. **Future-Proof**
- Ghost is moving away from HTML-based content
- Lexical is the modern standard for Ghost content
- Better compatibility with Ghost's editor features

### 4. **Performance**
- More efficient than HTML parsing
- Smaller payload sizes
- Faster sync operations

## Implementation Approach

### Option 1: TryGhost Libraries (RECOMMENDED)

**Libraries needed:**
```bash
npm install @tryghost/kg-html-to-lexical @tryghost/kg-markdown-html-renderer
```

**Conversion flow:**
```
Markdown → HTML (via @tryghost/kg-markdown-html-renderer) → Lexical (via @tryghost/kg-html-to-lexical)
```

**Advantages:**
- ✅ Already proven to work in CLI scripts
- ✅ Produces Ghost-compatible lexical format
- ✅ Handles all Ghost-specific features
- ✅ Minimal implementation effort
- ✅ Consistent with existing codebase

### Option 2: Direct @lexical/markdown (NOT RECOMMENDED)

**Libraries needed:**
```bash
npm install @lexical/markdown @lexical/headless lexical
```

**Challenges:**
- ❌ Requires setting up headless Lexical editor
- ❌ Need custom transformers for Ghost-specific features
- ❌ May not produce Ghost-compatible lexical structure
- ❌ Significant implementation complexity
- ❌ Additional dependencies and bundle size

## Required Changes for Obsidian Plugin

### 1. **Add Dependencies**
```json
{
  "dependencies": {
    "@tryghost/admin-api": "^1.14.0",
    "@tryghost/kg-html-to-lexical": "^6.0.0",
    "@tryghost/kg-markdown-html-renderer": "^6.0.0",
    "turndown": "^7.2.0"
  }
}
```

### 2. **Update ContentConverter**

Replace the current `createGhostPostData` method:

```typescript
// Current approach (HTML-based)
postData.html = this.markdownToHtml(markdownContent);
postData.lexical = null;

// New approach (Lexical-based)
const ghostHtml = renderMarkdownToHtml(markdownContent);
const lexicalContent = htmlToLexical(ghostHtml);
postData.lexical = JSON.stringify(lexicalContent);
postData.html = ghostHtml; // Keep for fallback
```

### 3. **Update Ghost API Calls**

Remove `source: 'html'` option since we're providing lexical content:

```typescript
// Current
const post = await this.api.posts.add(postData, { source: 'html' });

// New
const post = await this.api.posts.add(postData); // Ghost will use lexical by default
```

### 4. **Improve Reverse Conversion**

The existing lexical → markdown extraction is already good, but could be enhanced:

```typescript
// Enhanced lexical processing
if (post.lexical) {
  const lexicalDoc = JSON.parse(post.lexical);
  // Better extraction logic for various node types
  content = this.extractContentFromLexical(lexicalDoc);
}
```

## Implementation Timeline

### Phase 1: Basic Lexical Support (1-2 days)
- Add TryGhost dependencies
- Update ContentConverter to use lexical format
- Test basic markdown → lexical → Ghost workflow

### Phase 2: Enhanced Features (2-3 days)
- Improve lexical → markdown extraction
- Add support for advanced markdown features
- Comprehensive testing with various content types

### Phase 3: Optimization (1 day)
- Performance improvements
- Error handling enhancements
- Documentation updates

## Potential Challenges and Solutions

### 1. **Bundle Size**
- **Challenge**: Additional dependencies increase plugin size
- **Solution**: TryGhost libraries are already optimized and relatively small

### 2. **Compatibility**
- **Challenge**: Ensuring lexical format works with all Ghost versions
- **Solution**: TryGhost libraries are maintained by Ghost team, ensuring compatibility

### 3. **Testing**
- **Challenge**: Need comprehensive testing with various markdown features
- **Solution**: Existing test suite can be extended with lexical format tests

## Recommendation

**Implement lexical format syncing using TryGhost libraries immediately.** This approach:

1. **Leverages existing proven technology** from the CLI scripts
2. **Provides maximum stability and reliability** for content syncing
3. **Requires minimal implementation effort** (mostly copying existing code)
4. **Future-proofs the plugin** for Ghost's lexical-first approach
5. **Enables advanced markdown features** like proper callout handling

The TryGhost approach is the clear winner - it's already working, tested, and produces the exact lexical format that Ghost expects.
